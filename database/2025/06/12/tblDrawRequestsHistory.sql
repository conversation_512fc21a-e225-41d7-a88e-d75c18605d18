CREATE TABLE `tblDrawRequestsHistory` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `drawId` INT NOT NULL,
    `status` ENUM('pending', 'approved', 'rejected') NOT NULL,
    `submittedAt` DATETIME NOT NULL,
    `amountRequested` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `wireAmount` DECIMAL(15,2) NULL DEFAULT NULL,
    `wireSentDate` DATE NULL DEFAULT NULL,
    `createdAt` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    INDEX IX_DrawRequestsHistory_DrawId (drawId),
    CONSTRAINT FK_DrawRequestsHistory_Draw FOREIGN KEY (drawId)
        REFERENCES tblDrawRequests(id) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `tblDrawRequestCategories` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `drawId` INT NOT NULL,
    `categoryName` VARCHAR(255) NOT NULL,
    `description` TEXT NULL,
    `order` INT UNSIGNED NOT NULL DEFAULT 1,
    `createdAt` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updatedAt` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX IX_DrawRequestCategories_Draw_Order (drawId, `order`),
    CONSTRAINT FK_DrawRequestCategories_Draw FOREIGN KEY (drawId)
        REFERENCES tblDrawRequests(id) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci;

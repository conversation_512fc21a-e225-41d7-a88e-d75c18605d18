<?php
namespace models\composite\oDrawManagement;

use models\types\strongType;
use models\lendingwise\tblDrawRequests_h;
use models\lendingwise\tblDrawRequestLineItems_h;
use models\lendingwise\db\tblDrawRequestLineItems_h_db;
use models\composite\oDrawManagement\BorrowerDrawLineItem_h;
use models\standard\Dates;

class DrawRequests_h extends strongType
{
    /**
     * @var int|null The ID of the history record.
     */
    public ?int $id = null;

    /**
     * @var int|null The ID of the original draw request.
     */
    public ?int $drawId = null;

    /**
     * @var string|null The status of the draw request at the time of history creation.
     */
    public ?string $status = null;

    /**
     * @var string|null The date when the draw request was submitted.
     */
    public ?string $submittedAt = null;

    /**
     * @var float The amount requested.
     */
    public ?float $amountRequested = 0.00;

    /**
     * @var float The amount approved.
     */
    public ?float $amountApproved = 0.00;

    /**
     * @var float|null The wire amount (can be null if not yet wired).
     */
    public ?float $wireAmount = null;

    /**
     * @var string|null The date when the wire was sent.
     */
    public ?string $wireSentDate = null;

    /**
     * @var string|null The creation date of the history record.
     */
    public ?string $createdAt = null;

    /**
     * @var tblDrawRequests_h|null The database table object for the draw request history.
     */
    public ?tblDrawRequests_h $drawRequestHistory = null;

    /**
     * @var BorrowerDrawLineItem_h[] Array of line item history objects associated with this draw request history.
     */
    public array $lineItems = [];

    // Status constants
    public const STATUS_PENDING = 'pending';
    public const STATUS_APPROVED = 'approved';
    public const STATUS_REJECTED = 'rejected';

    /**
     * DrawRequests_h constructor.
     * @param tblDrawRequests_h|null $drawRequestHistory The database draw request history object to initialize from.
     */
    public function __construct(?tblDrawRequests_h $drawRequestHistory = null) {
        if ($drawRequestHistory == null) $drawRequestHistory = new tblDrawRequests_h();
        $this->setProperties($drawRequestHistory);
        if ($drawRequestHistory->id) $this->loadLineItems();
    }

    /**
     * Saves the current draw request history object to the database.
     * @param array|null $drawRequestData Optional data to set before saving.
     * @return array The result of the save operation.
     */
    public function save(?array $drawRequestData = null): array {
        if(!empty($drawRequestData)) $this->setFromArray($drawRequestData);
        $saved = $this->drawRequestHistory->save();
        $this->id = $this->drawRequestHistory->id;
        return $saved;
    }

    /**
     * Sets properties from the database object.
     * @param tblDrawRequests_h $drawRequestHistory The database object.
     * @return void
     */
    private function setProperties(tblDrawRequests_h $drawRequestHistory): void {
        $this->drawRequestHistory = $drawRequestHistory;
        $this->id = $drawRequestHistory->id;
        $this->drawId = $drawRequestHistory->drawId;
        $this->status = $drawRequestHistory->status;
        $this->submittedAt = $drawRequestHistory->submittedAt;
        $this->amountRequested = $drawRequestHistory->amountRequested;
        $this->amountApproved = $drawRequestHistory->amountApproved;
        $this->wireAmount = $drawRequestHistory->wireAmount;
        $this->wireSentDate = $drawRequestHistory->wireSentDate;
        $this->createdAt = $drawRequestHistory->createdAt;
    }

    /**
     * Sets properties from an array.
     * @param array $data The data array.
     * @return void
     */
    private function setFromArray(array $data): void {
        if (isset($data['drawId'])) $this->drawRequestHistory->drawId = $data['drawId'];
        if (isset($data['status'])) $this->drawRequestHistory->status = $data['status'];
        if (isset($data['submittedAt'])) $this->drawRequestHistory->submittedAt = $data['submittedAt'];
        if (isset($data['amountRequested'])) $this->drawRequestHistory->amountRequested = $data['amountRequested'];
        if (isset($data['amountApproved'])) $this->drawRequestHistory->amountApproved = $data['amountApproved'];
        if (isset($data['wireAmount'])) $this->drawRequestHistory->wireAmount = $data['wireAmount'];
        if (isset($data['wireSentDate'])) $this->drawRequestHistory->wireSentDate = $data['wireSentDate'];
    }

    /**
     * Load line items associated with this draw request history record.
     * @return void
     */
    private function loadLineItems(): void {
        if (!$this->id) {
            return;
        }

        $lineItemsData = tblDrawRequestLineItems_h::GetAll([
            tblDrawRequestLineItems_h_db::COLUMN_RECORDID => $this->id
        ]);

        $this->lineItems = [];
        foreach ($lineItemsData as $lineItemData) {
            $this->lineItems[] = new BorrowerDrawLineItem_h($lineItemData);
        }
    }

    /**
     * Gets the database object.
     * @return tblDrawRequests_h The database object.
     */
    public function getDbObject(): tblDrawRequests_h {
        return $this->drawRequestHistory;
    }

    /**
     * Get all line items associated with this draw request history.
     * @return BorrowerDrawLineItem_h[] Array of line item history objects.
     */
    public function getLineItems(): array {
        return $this->lineItems;
    }

    /**
     * Add a line item history record to this draw request history.
     * @param array $lineItemData The line item data.
     * @return BorrowerDrawLineItem_h The created line item history object.
     */
    public function addLineItem(array $lineItemData): BorrowerDrawLineItem_h {
        $lineItemData['recordId'] = $this->id;
        $lineItem = new BorrowerDrawLineItem_h();
        $lineItem->save($lineItemData);
        $this->lineItems[] = $lineItem;
        return $lineItem;
    }

    /**
     * Create a new draw request history record when a draw request is approved and submitted.
     * @param int $drawId The original draw request ID.
     * @param string $status The status of the draw request.
     * @param float $amountRequested The amount requested.
     * @param array $lineItemsData Array of line item data to save as history.
     * @return self The created draw request history object.
     */
    public static function createHistoryRecord(int $drawId, string $status, float $amountRequested, array $lineItemsData = []): self {
        $historyData = [
            'drawId' => $drawId,
            'status' => $status,
            'submittedAt' => Dates::Timestamp(),
            'amountRequested' => $amountRequested
        ];

        $history = new self();
        $history->save($historyData);

        // Save line items history
        foreach ($lineItemsData as $lineItemData) {
            $history->addLineItem($lineItemData);
        }

        return $history;
    }

    /**
     * Get all history records for a specific draw request.
     * @param int $drawId The draw request ID.
     * @return self[] Array of draw request history objects.
     */
    public static function getHistoryByDrawId(int $drawId): array {
        $historyData = tblDrawRequests_h::GetAll([
            'drawId' => $drawId
        ]);

        $historyRecords = [];
        foreach ($historyData as $record) {
            $historyRecords[] = new self($record);
        }

        return $historyRecords;
    }

    /**
     * Converts the draw request history object to an associative array.
     * @return array An associative array representation of the draw request history.
     */
    public function toArray(): array {
        $lineItemsData = [];
        foreach ($this->lineItems as $lineItem) {
            $lineItemsData[] = $lineItem->toArray();
        }

        return [
            "id" => $this->id,
            "drawId" => $this->drawId,
            "status" => $this->status,
            "submittedAt" => $this->submittedAt,
            "amountRequested" => $this->amountRequested,
            "amountApproved" => $this->amountApproved,
            "wireAmount" => $this->wireAmount,
            "wireSentDate" => $this->wireSentDate,
            "createdAt" => $this->createdAt,
            "lineItems" => $lineItemsData
        ];
    }
}
